{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/lectures/:path*{(\\\\.json)}?", "originalSource": "/lectures/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/assignments/:path*{(\\\\.json)}?", "originalSource": "/assignments/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/messages/:path*{(\\\\.json)}?", "originalSource": "/messages/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qrUUpV8JtBqJAAYOWjHh1Co06QbvQXmcWVuQrBJ4WkU=", "__NEXT_PREVIEW_MODE_ID": "1d900f986bb01c82a1ace19844daa45f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fb58263ff8059c1a7a8d2f4bec325f2cfb39b42a4577c37adc4843745031b5a5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f65f784ac26cf46fa0b7ecac8227ea8ea74918939727924a495f6bb4c0bd41a0"}}}, "instrumentation": null, "functions": {}}