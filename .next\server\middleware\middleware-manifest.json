{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/lectures/:path*{(\\\\.json)}?", "originalSource": "/lectures/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/assignments/:path*{(\\\\.json)}?", "originalSource": "/assignments/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/messages/:path*{(\\\\.json)}?", "originalSource": "/messages/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qrUUpV8JtBqJAAYOWjHh1Co06QbvQXmcWVuQrBJ4WkU=", "__NEXT_PREVIEW_MODE_ID": "e585a1713eb6929cb12b9e8a96791d51", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0fc7452e0b715e4fd11d5561db55d3fe3c6cda560245114d4097150e2ff4083f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f26452a03c50698f82bdc24bc1859a88a8c34232ae3f62878d14850ebccab058"}}}, "instrumentation": null, "functions": {}}