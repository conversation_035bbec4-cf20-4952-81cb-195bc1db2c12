{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/lectures/:path*{(\\\\.json)}?", "originalSource": "/lectures/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/assignments/:path*{(\\\\.json)}?", "originalSource": "/assignments/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/messages/:path*{(\\\\.json)}?", "originalSource": "/messages/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qrUUpV8JtBqJAAYOWjHh1Co06QbvQXmcWVuQrBJ4WkU=", "__NEXT_PREVIEW_MODE_ID": "8b393dc1fce0fdae2b0e6c902febb15c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "262cb3952bf4512c41a5838af5caa08f3082907836f6cbb00afd5010e334205b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f13ca052840abf2aedd18e68e894f7d89a58ebdfda758f994784117c0171bd30"}}}, "instrumentation": null, "functions": {}}