# 🎓 المنصة التعليمية الإلكترونية - المعهد العالي للإدارة والحاسب الآلي

منصة تعليمية شاملة ومتطورة مصممة خصيصاً للمعهد العالي للإدارة والحاسب الآلي، توفر بيئة تعليمية متكاملة للطلاب وأعضاء هيئة التدريس والإدارة.

## 🌟 المميزات الرئيسية

### 👨‍🎓 للطلاب
- **متابعة المحاضرات**: الوصول السهل لجميع المحاضرات المرفوعة بصيغ مختلفة
- **إدارة التكليفات**: تسليم الواجبات ومتابعة الدرجات والتعليقات
- **نظام الرسائل**: التواصل المباشر مع أعضاء هيئة التدريس
- **الإشعارات الذكية**: تنبيهات فورية للمحاضرات الجديدة ومواعيد التسليم
- **لوحة تحكم شخصية**: نظرة شاملة على الأداء الأكاديمي

### 👨‍🏫 لأعضاء هيئة التدريس
- **إدارة المحاضرات**: رفع وتنظيم المحاضرات بسهولة
- **نظام التكليفات**: إنشاء وإدارة التكليفات مع نظام تقييم شامل
- **متابعة الطلاب**: مراقبة أداء الطلاب وتقدمهم الأكاديمي
- **التواصل**: نظام رسائل متطور للتواصل مع الطلاب
- **التقارير**: إحصائيات مفصلة عن أداء الطلاب

### 👨‍💼 للإدارة
- **إدارة المستخدمين**: تسجيل وإدارة الطلاب وأعضاء هيئة التدريس
- **إدارة المواد**: تنظيم المواد الدراسية والفرق والشعب
- **التقارير الشاملة**: إحصائيات مفصلة عن النظام بالكامل
- **إعدادات النظام**: تحكم كامل في إعدادات المنصة

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 14 مع TypeScript
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL مع Prisma ORM
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS مع Shadcn/ui
- **Icons**: Lucide React
- **Deployment**: Vercel (مُوصى به)

## 🚀 البدء السريع

### المتطلبات الأساسية
- Node.js 18+
- PostgreSQL
- npm أو yarn

### التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/himconline.git
cd himconline
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد قاعدة البيانات**
```bash
# إنشاء قاعدة بيانات PostgreSQL جديدة
createdb himconline

# تحديث ملف .env بمعلومات قاعدة البيانات
cp .env.example .env
```

4. **تحديث متغيرات البيئة**
```env
DATABASE_URL="postgresql://username:password@localhost:5432/himconline?schema=public"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"
```

5. **تشغيل migrations**
```bash
npx prisma generate
npx prisma db push
```

6. **تشغيل المشروع**
```bash
npm run dev
```

7. **فتح المتصفح**
افتح [http://localhost:3000](http://localhost:3000) لرؤية المنصة.

## 🔐 بيانات الدخول التجريبية

للاختبار، يمكنك استخدام البيانات التالية:

### طالب
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

### مدرس
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

### إداري
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

## 📁 هيكل المشروع

```
himconline/
├── src/
│   ├── app/                    # صفحات Next.js
│   │   ├── dashboard/          # لوحات التحكم
│   │   │   ├── student/        # لوحة الطالب
│   │   │   ├── teacher/        # لوحة المدرس
│   │   │   └── admin/          # لوحة الإدارة
│   │   ├── login/              # صفحة تسجيل الدخول
│   │   └── api/                # API Routes
│   ├── components/             # مكونات React
│   │   └── ui/                 # مكونات واجهة المستخدم
│   ├── lib/                    # مكتبات مساعدة
│   │   ├── auth.ts             # إعدادات NextAuth
│   │   ├── prisma.ts           # إعداد Prisma
│   │   └── utils.ts            # دوال مساعدة
│   └── generated/              # ملفات Prisma المولدة
├── prisma/
│   └── schema.prisma           # مخطط قاعدة البيانات
├── public/                     # الملفات الثابتة
└── README.md
```

## 🗄️ مخطط قاعدة البيانات

المنصة تستخدم قاعدة بيانات PostgreSQL مع النماذج التالية:

- **User**: المستخدمون الأساسيون
- **Student**: بيانات الطلاب
- **Teacher**: بيانات أعضاء هيئة التدريس
- **Admin**: بيانات الإداريين
- **Subject**: المواد الدراسية
- **Lecture**: المحاضرات
- **Assignment**: التكليفات
- **AssignmentSubmission**: تسليم التكليفات
- **Message**: الرسائل
- **Notification**: الإشعارات

## 🔧 الأوامر المفيدة

```bash
# تشغيل المشروع في وضع التطوير
npm run dev

# بناء المشروع للإنتاج
npm run build

# تشغيل المشروع في وضع الإنتاج
npm start

# فحص الكود
npm run lint

# تحديث قاعدة البيانات
npx prisma db push

# إعادة تولید Prisma client
npx prisma generate

# فتح Prisma Studio
npx prisma studio
```

## 🌐 النشر

### Vercel (مُوصى به)
1. ادفع الكود إلى GitHub
2. اربط المشروع بـ Vercel
3. أضف متغيرات البيئة في إعدادات Vercel
4. انشر المشروع

### متغيرات البيئة للإنتاج
```env
DATABASE_URL="your-production-database-url"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-production-secret"
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى Branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **المطور**: [اسمك]
- **البريد الإلكتروني**: <EMAIL>
- **المشروع**: [https://github.com/your-username/himconline](https://github.com/your-username/himconline)

## 🙏 شكر وتقدير

- [Next.js](https://nextjs.org/) - إطار العمل الأساسي
- [Tailwind CSS](https://tailwindcss.com/) - إطار عمل CSS
- [Prisma](https://prisma.io/) - ORM لقاعدة البيانات
- [NextAuth.js](https://next-auth.js.org/) - نظام المصادقة
- [Shadcn/ui](https://ui.shadcn.com/) - مكونات واجهة المستخدم
- [Lucide](https://lucide.dev/) - مكتبة الأيقونات

---

**المعهد العالي للإدارة والحاسب الآلي** - منصة تعليمية متطورة لمستقبل أفضل 🎓
