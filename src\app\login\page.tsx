"use client"

import { useState } from "react"
import { signIn, getSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BookOpen, Mail, Lock, User } from "lucide-react"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [userType, setUserType] = useState("STUDENT")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const result = await signIn("credentials", {
        email,
        password,
        userType,
        redirect: false
      })

      if (result?.error) {
        setError("بيانات الدخول غير صحيحة")
      } else {
        // الحصول على بيانات الجلسة لتحديد نوع المستخدم
        const session = await getSession()
        if (session?.user?.role) {
          // توجيه المستخدم حسب نوعه
          switch (session.user.role) {
            case "ADMIN":
              router.push("/dashboard/admin")
              break
            case "TEACHER":
              router.push("/dashboard/teacher")
              break
            case "STUDENT":
              router.push("/dashboard/student")
              break
            default:
              router.push("/dashboard")
          }
        }
      }
    } catch (error) {
      setError("حدث خطأ أثناء تسجيل الدخول")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 mb-4">
            <div className="bg-blue-600 text-white p-2 rounded-lg">
              <BookOpen className="h-6 w-6" />
            </div>
            <span className="text-xl font-bold text-gray-900">المعهد العالي للإدارة والحاسب الآلي</span>
          </Link>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">تسجيل الدخول</h1>
          <p className="text-gray-600">ادخل بياناتك للوصول إلى المنصة التعليمية</p>
        </div>

        {/* Login Form */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center">تسجيل الدخول</CardTitle>
            <CardDescription className="text-center">
              اختر نوع المستخدم وادخل بيانات الدخول
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* User Type Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">نوع المستخدم</label>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    type="button"
                    variant={userType === "STUDENT" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUserType("STUDENT")}
                    className="flex items-center gap-2"
                  >
                    <User className="h-4 w-4" />
                    طالب
                  </Button>
                  <Button
                    type="button"
                    variant={userType === "TEACHER" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUserType("TEACHER")}
                    className="flex items-center gap-2"
                  >
                    <BookOpen className="h-4 w-4" />
                    مدرس
                  </Button>
                  <Button
                    type="button"
                    variant={userType === "ADMIN" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setUserType("ADMIN")}
                    className="flex items-center gap-2"
                  >
                    <User className="h-4 w-4" />
                    إداري
                  </Button>
                </div>
              </div>

              {/* Email Input */}
              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium text-gray-700">
                  البريد الإلكتروني
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="ادخل البريد الإلكتروني"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              {/* Password Input */}
              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium text-gray-700">
                  كلمة المرور
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="ادخل كلمة المرور"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={isLoading}
              >
                {isLoading ? "جاري تسجيل الدخول..." : "تسجيل الدخول"}
              </Button>
            </form>

            {/* Demo Credentials */}
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">بيانات تجريبية:</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <p><strong>طالب:</strong> <EMAIL></p>
                <p><strong>مدرس:</strong> <EMAIL></p>
                <p><strong>إداري:</strong> <EMAIL></p>
                <p><strong>كلمة المرور:</strong> 123456</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6">
          <Link href="/" className="text-sm text-blue-600 hover:text-blue-700">
            العودة إلى الصفحة الرئيسية
          </Link>
        </div>
      </div>
    </div>
  )
}
