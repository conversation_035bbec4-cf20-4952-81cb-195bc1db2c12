"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { MessageSquare, Search, Send, User, ArrowRight, Clock, Plus } from "lucide-react"
import Link from "next/link"

// Mock data - في التطبيق الحقيقي سيتم جلب البيانات من API
const mockMessages = [
  {
    id: "1",
    content: "مرحباً، لدي استفسار حول المحاضرة الأخيرة في مادة البرمجة",
    sender: "د. أحمد محمد",
    subject: "مقدمة في البرمجة",
    date: "2024-01-20T10:30:00",
    isRead: false,
    type: "LECTURE"
  },
  {
    id: "2",
    content: "تم تصحيح تكليف قاعدة البيانات، يمكنك مراجعة النتيجة",
    sender: "د. فاطمة علي",
    subject: "قواعد البيانات",
    date: "2024-01-19T14:15:00",
    isRead: true,
    type: "ASSIGNMENT"
  },
  {
    id: "3",
    content: "إعلان مهم: سيتم تأجيل محاضرة الغد إلى الأسبوع القادم",
    sender: "إدارة المعهد",
    subject: "إعلان عام",
    date: "2024-01-18T09:00:00",
    isRead: true,
    type: "ANNOUNCEMENT"
  },
  {
    id: "4",
    content: "شكراً لك على المشاركة الفعالة في المحاضرة اليوم",
    sender: "د. محمد حسن",
    subject: "تحليل النظم",
    date: "2024-01-17T16:45:00",
    isRead: false,
    type: "GENERAL"
  }
]

export default function MessagesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredMessages, setFilteredMessages] = useState(mockMessages)
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null)

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/login")
      return
    }
  }, [session, status, router])

  useEffect(() => {
    const filtered = mockMessages.filter(message =>
      message.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
      message.sender.toLowerCase().includes(searchTerm.toLowerCase()) ||
      message.subject.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredMessages(filtered)
  }, [searchTerm])

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "LECTURE":
        return "bg-blue-100 text-blue-800"
      case "ASSIGNMENT":
        return "bg-green-100 text-green-800"
      case "ANNOUNCEMENT":
        return "bg-red-100 text-red-800"
      case "GENERAL":
        return "bg-purple-100 text-purple-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case "LECTURE":
        return "محاضرة"
      case "ASSIGNMENT":
        return "تكليف"
      case "ANNOUNCEMENT":
        return "إعلان"
      case "GENERAL":
        return "عام"
      default:
        return "غير محدد"
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return "منذ دقائق"
    } else if (diffInHours < 24) {
      return `منذ ${diffInHours} ساعة`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `منذ ${diffInDays} يوم`
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="flex items-center text-blue-600 hover:text-blue-700">
                <ArrowRight className="h-5 w-5 ml-2" />
                العودة للوحة التحكم
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                <Plus className="h-4 w-4 ml-2" />
                رسالة جديدة
              </Button>
              <div className="bg-purple-600 text-white p-2 rounded-lg">
                <MessageSquare className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">الرسائل</h1>
                <p className="text-sm text-gray-600">صندوق الرسائل والإشعارات</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Messages List */}
          <div className="lg:col-span-2">
            {/* Search Bar */}
            <div className="mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="البحث في الرسائل..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Messages */}
            <div className="space-y-4">
              {filteredMessages.map((message) => (
                <Card 
                  key={message.id} 
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    !message.isRead ? 'border-blue-200 bg-blue-50' : ''
                  } ${selectedMessage === message.id ? 'ring-2 ring-blue-500' : ''}`}
                  onClick={() => setSelectedMessage(message.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="bg-purple-100 p-1 rounded-full">
                            <User className="h-3 w-3 text-purple-600" />
                          </div>
                          <span className="font-medium text-sm">{message.sender}</span>
                          <span className={`text-xs px-2 py-1 rounded-full ${getTypeColor(message.type)}`}>
                            {getTypeText(message.type)}
                          </span>
                          {!message.isRead && (
                            <span className="w-2 h-2 bg-blue-600 rounded-full"></span>
                          )}
                        </div>
                        <p className="text-gray-900 mb-2">{message.content}</p>
                        <div className="flex items-center text-xs text-gray-500 space-x-4">
                          <span className="flex items-center">
                            <MessageSquare className="h-3 w-3 ml-1" />
                            {message.subject}
                          </span>
                          <span className="flex items-center">
                            <Clock className="h-3 w-3 ml-1" />
                            {formatDate(message.date)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* No Results */}
            {filteredMessages.length === 0 && (
              <div className="text-center py-12">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد رسائل</h3>
                <p className="text-gray-600">لم يتم العثور على رسائل تطابق البحث</p>
              </div>
            )}
          </div>

          {/* Message Details / Stats */}
          <div className="space-y-6">
            {/* Selected Message Details */}
            {selectedMessage && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">تفاصيل الرسالة</CardTitle>
                </CardHeader>
                <CardContent>
                  {(() => {
                    const message = mockMessages.find(m => m.id === selectedMessage)
                    if (!message) return null
                    
                    return (
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium text-gray-700">المرسل:</label>
                          <p className="text-gray-900">{message.sender}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">الموضوع:</label>
                          <p className="text-gray-900">{message.subject}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">التاريخ:</label>
                          <p className="text-gray-900">
                            {new Date(message.date).toLocaleDateString('ar-EG')} - {new Date(message.date).toLocaleTimeString('ar-EG')}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">المحتوى:</label>
                          <p className="text-gray-900 bg-gray-50 p-3 rounded-lg">{message.content}</p>
                        </div>
                        <Button className="w-full">
                          <Send className="h-4 w-4 ml-2" />
                          رد على الرسالة
                        </Button>
                      </div>
                    )
                  })()}
                </CardContent>
              </Card>
            )}

            {/* Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">إحصائيات الرسائل</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">إجمالي الرسائل</span>
                    <span className="font-bold text-gray-900">{mockMessages.length}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">غير مقروءة</span>
                    <span className="font-bold text-blue-600">
                      {mockMessages.filter(m => !m.isRead).length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">إعلانات</span>
                    <span className="font-bold text-red-600">
                      {mockMessages.filter(m => m.type === "ANNOUNCEMENT").length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">تكليفات</span>
                    <span className="font-bold text-green-600">
                      {mockMessages.filter(m => m.type === "ASSIGNMENT").length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">إجراءات سريعة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <MessageSquare className="h-4 w-4 ml-2" />
                    تحديد الكل كمقروء
                  </Button>
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <User className="h-4 w-4 ml-2" />
                    رسالة لمدرس
                  </Button>
                  <Button variant="outline" className="w-full justify-start" size="sm">
                    <Send className="h-4 w-4 ml-2" />
                    الرسائل المرسلة
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
