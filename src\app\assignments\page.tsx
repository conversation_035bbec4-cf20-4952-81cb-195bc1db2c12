"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Award, Search, Upload, Calendar, User, ArrowRight, Clock, CheckCircle } from "lucide-react"
import Link from "next/link"

// Mock data - في التطبيق الحقيقي سيتم جلب البيانات من API
const mockAssignments = [
  {
    id: "1",
    title: "تمرين البرمجة الأول",
    description: "كتابة برنامج بسيط بـ Python",
    subject: "مقدمة في البرمجة",
    teacher: "د. أحمد محمد",
    dueDate: "2024-01-25",
    maxGrade: 100,
    isSubmitted: false,
    status: "pending",
    timeLeft: "3 أيام"
  },
  {
    id: "2",
    title: "مشروع قاعدة البيانات",
    description: "تصميم قاعدة بيانات لنظام إدارة المكتبة",
    subject: "قواعد البيانات",
    teacher: "د. فاطمة علي",
    dueDate: "2024-01-30",
    maxGrade: 150,
    isSubmitted: true,
    status: "submitted",
    grade: 135,
    timeLeft: "8 أيام"
  },
  {
    id: "3",
    title: "تحليل النظم",
    description: "تحليل نظام إدارة المستشفى",
    subject: "تحليل النظم",
    teacher: "د. محمد حسن",
    dueDate: "2024-01-20",
    maxGrade: 100,
    isSubmitted: true,
    status: "graded",
    grade: 85,
    timeLeft: "منتهي"
  }
]

export default function AssignmentsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredAssignments, setFilteredAssignments] = useState(mockAssignments)

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/login")
      return
    }
  }, [session, status, router])

  useEffect(() => {
    const filtered = mockAssignments.filter(assignment =>
      assignment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assignment.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      assignment.teacher.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredAssignments(filtered)
  }, [searchTerm])

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800"
      case "submitted":
        return "bg-blue-100 text-blue-800"
      case "graded":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "في الانتظار"
      case "submitted":
        return "تم التسليم"
      case "graded":
        return "تم التقييم"
      default:
        return "غير محدد"
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="flex items-center text-blue-600 hover:text-blue-700">
                <ArrowRight className="h-5 w-5 ml-2" />
                العودة للوحة التحكم
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-green-600 text-white p-2 rounded-lg">
                <Award className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">التكليفات</h1>
                <p className="text-sm text-gray-600">جميع التكليفات والواجبات</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="البحث في التكليفات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Assignments Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAssignments.map((assignment) => (
            <Card key={assignment.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Award className="h-5 w-5 text-green-600" />
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(assignment.status)}`}>
                    {getStatusText(assignment.status)}
                  </span>
                </div>
                <CardTitle className="text-lg">{assignment.title}</CardTitle>
                <CardDescription>{assignment.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Award className="h-4 w-4 ml-2" />
                    {assignment.subject}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <User className="h-4 w-4 ml-2" />
                    {assignment.teacher}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 ml-2" />
                    موعد التسليم: {new Date(assignment.dueDate).toLocaleDateString('ar-EG')}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="h-4 w-4 ml-2" />
                    {assignment.timeLeft}
                  </div>
                  
                  {assignment.status === "graded" && assignment.grade && (
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-green-800">الدرجة:</span>
                        <span className="text-lg font-bold text-green-600">
                          {assignment.grade}/{assignment.maxGrade}
                        </span>
                      </div>
                    </div>
                  )}
                  
                  <div className="pt-4">
                    {assignment.status === "pending" && (
                      <Button className="w-full">
                        <Upload className="h-4 w-4 ml-2" />
                        تسليم التكليف
                      </Button>
                    )}
                    {assignment.status === "submitted" && (
                      <Button variant="outline" className="w-full">
                        <CheckCircle className="h-4 w-4 ml-2" />
                        عرض التسليم
                      </Button>
                    )}
                    {assignment.status === "graded" && (
                      <Button variant="outline" className="w-full">
                        <CheckCircle className="h-4 w-4 ml-2" />
                        عرض النتيجة
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredAssignments.length === 0 && (
          <div className="text-center py-12">
            <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد تكليفات</h3>
            <p className="text-gray-600">لم يتم العثور على تكليفات تطابق البحث</p>
          </div>
        )}

        {/* Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 p-3 rounded-full">
                  <Award className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">{mockAssignments.length}</p>
                  <p className="text-gray-600">إجمالي التكليفات</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="bg-yellow-100 p-3 rounded-full">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {mockAssignments.filter(a => a.status === "pending").length}
                  </p>
                  <p className="text-gray-600">في الانتظار</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="bg-green-100 p-3 rounded-full">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {mockAssignments.filter(a => a.status === "submitted" || a.status === "graded").length}
                  </p>
                  <p className="text-gray-600">تم التسليم</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 p-3 rounded-full">
                  <Award className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {mockAssignments.filter(a => a.status === "graded").length > 0 
                      ? Math.round(mockAssignments.filter(a => a.status === "graded").reduce((acc, a) => acc + (a.grade || 0), 0) / mockAssignments.filter(a => a.status === "graded").length)
                      : 0}%
                  </p>
                  <p className="text-gray-600">متوسط الدرجات</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
