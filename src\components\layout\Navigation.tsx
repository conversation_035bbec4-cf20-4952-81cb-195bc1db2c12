"use client"

import { useSession, signOut } from "next-auth/react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "@/components/ui/button"
import { 
  BookOpen, 
  Award, 
  MessageSquare, 
  Users, 
  Settings, 
  LogOut, 
  Home,
  User,
  Bell
} from "lucide-react"

const studentNavItems = [
  { href: "/dashboard/student", label: "لوحة التحكم", icon: Home },
  { href: "/lectures", label: "المحاضرات", icon: BookOpen },
  { href: "/assignments", label: "التكليفات", icon: Award },
  { href: "/messages", label: "الرسائل", icon: MessageSquare },
]

const teacherNavItems = [
  { href: "/dashboard/teacher", label: "لوحة التحكم", icon: Home },
  { href: "/lectures", label: "المحاضرات", icon: BookOpen },
  { href: "/assignments", label: "التكليفات", icon: Award },
  { href: "/messages", label: "الرسائل", icon: MessageSquare },
  { href: "/students", label: "الطلاب", icon: Users },
]

const adminNavItems = [
  { href: "/dashboard/admin", label: "لوحة التحكم", icon: Home },
  { href: "/users", label: "إدارة المستخدمين", icon: Users },
  { href: "/subjects", label: "المواد الدراسية", icon: BookOpen },
  { href: "/reports", label: "التقارير", icon: Award },
  { href: "/settings", label: "الإعدادات", icon: Settings },
]

export default function Navigation() {
  const { data: session } = useSession()
  const pathname = usePathname()

  if (!session) return null

  const getNavItems = () => {
    switch (session.user.role) {
      case "STUDENT":
        return studentNavItems
      case "TEACHER":
        return teacherNavItems
      case "ADMIN":
        return adminNavItems
      default:
        return []
    }
  }

  const navItems = getNavItems()

  return (
    <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="bg-blue-600 text-white p-2 rounded-lg">
              <BookOpen className="h-6 w-6" />
            </div>
            <div className="hidden md:block">
              <h1 className="text-lg font-bold text-gray-900">المعهد العالي</h1>
              <p className="text-xs text-gray-600">للإدارة والحاسب الآلي</p>
            </div>
          </Link>

          {/* Navigation Items */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href
              
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    isActive
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.label}</span>
                </Link>
              )
            })}
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm">
              <Bell className="h-4 w-4" />
            </Button>
            
            <div className="flex items-center space-x-3">
              <div className="hidden md:block text-right">
                <p className="text-sm font-medium text-gray-900">{session.user.name}</p>
                <p className="text-xs text-gray-600">
                  {session.user.role === "STUDENT" && "طالب"}
                  {session.user.role === "TEACHER" && "مدرس"}
                  {session.user.role === "ADMIN" && "إداري"}
                </p>
              </div>
              
              <div className="bg-gray-200 p-2 rounded-full">
                <User className="h-4 w-4 text-gray-600" />
              </div>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => signOut({ callbackUrl: "/" })}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <LogOut className="h-4 w-4" />
              <span className="hidden md:inline mr-2">خروج</span>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden pb-4">
          <div className="flex space-x-2 overflow-x-auto">
            {navItems.map((item) => {
              const Icon = item.icon
              const isActive = pathname === item.href
              
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium whitespace-nowrap transition-colors ${
                    isActive
                      ? "bg-blue-100 text-blue-700"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{item.label}</span>
                </Link>
              )
            })}
          </div>
        </div>
      </div>
    </nav>
  )
}
