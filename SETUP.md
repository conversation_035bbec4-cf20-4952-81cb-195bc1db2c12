# 🚀 دليل التثبيت والتشغيل - المنصة التعليمية

## 📋 المتطلبات الأساسية

قبل البدء، تأكد من تثبيت:
- **Node.js** 18 أو أحدث
- **npm** أو **yarn**
- **PostgreSQL** (اختياري - يمكن استخدام SQLite للتطوير)

## ⚡ التثبيت السريع

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd himconline
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. إعداد متغيرات البيئة
```bash
cp .env.example .env
```

قم بتحديث ملف `.env` بالمعلومات المناسبة:
```env
DATABASE_URL="postgresql://username:password@localhost:5432/himconline?schema=public"
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"
```

### 4. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات (PostgreSQL)
createdb himconline

# تطبيق المخطط
npx prisma db push

# توليد Prisma client
npx prisma generate

# إضافة البيانات التجريبية (اختياري)
npm run db:seed
```

### 5. تشغيل المشروع
```bash
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح.

## 🔐 بيانات الدخول التجريبية

### طالب
- **البريد**: <EMAIL>
- **كلمة المرور**: 123456

### مدرس
- **البريد**: <EMAIL>
- **كلمة المرور**: 123456

### إداري
- **البريد**: <EMAIL>
- **كلمة المرور**: 123456

## 🛠️ أوامر مفيدة

```bash
# تشغيل المشروع
npm run dev

# بناء للإنتاج
npm run build

# تشغيل الإنتاج
npm start

# إدارة قاعدة البيانات
npm run db:push      # تطبيق التغييرات
npm run db:generate  # توليد Prisma client
npm run db:seed      # إضافة بيانات تجريبية
npm run db:studio    # فتح Prisma Studio

# فحص الكود
npm run lint
```

## 🗄️ إعداد قاعدة البيانات

### PostgreSQL (مُوصى به للإنتاج)
```bash
# تثبيت PostgreSQL
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib

# macOS
brew install postgresql

# إنشاء قاعدة البيانات
createdb himconline

# تحديث .env
DATABASE_URL="postgresql://username:password@localhost:5432/himconline?schema=public"
```

### SQLite (للتطوير السريع)
```bash
# تحديث .env
DATABASE_URL="file:./dev.db"
```

## 🌐 النشر

### Vercel (مُوصى به)
1. ادفع الكود إلى GitHub
2. اربط المشروع بـ Vercel
3. أضف متغيرات البيئة
4. انشر المشروع

### متغيرات البيئة للإنتاج
```env
DATABASE_URL="your-production-database-url"
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-production-secret"
```

## 🔧 استكشاف الأخطاء

### مشكلة في قاعدة البيانات
```bash
# إعادة تعيين قاعدة البيانات
npx prisma db push --force-reset
npx prisma generate
npm run db:seed
```

### مشكلة في التبعيات
```bash
# حذف node_modules وإعادة التثبيت
rm -rf node_modules package-lock.json
npm install
```

### مشكلة في Prisma
```bash
# إعادة توليد Prisma client
npx prisma generate
```

## 📁 هيكل المشروع

```
himconline/
├── src/
│   ├── app/                 # صفحات Next.js
│   ├── components/          # مكونات React
│   ├── lib/                 # مكتبات مساعدة
│   └── generated/           # ملفات Prisma
├── prisma/
│   ├── schema.prisma        # مخطط قاعدة البيانات
│   └── seed.ts             # بيانات تجريبية
├── public/                  # ملفات ثابتة
└── README.md
```

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء branch جديد
3. إجراء التغييرات
4. إرسال Pull Request

## 📞 الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملف README.md
2. راجع Issues في GitHub
3. تواصل مع فريق التطوير

---

**المعهد العالي للإدارة والحاسب الآلي** 🎓
