"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { BookOpen, Search, Download, Play, Calendar, User, ArrowRight } from "lucide-react"
import Link from "next/link"

// Mock data - في التطبيق الحقيقي سيتم جلب البيانات من API
const mockLectures = [
  {
    id: "1",
    title: "مقدمة في البرمجة - المحاضرة 1",
    description: "التعريف بالبرمجة وأساسياتها",
    subject: "مقدمة في البرمجة",
    teacher: "د. أحمد محمد",
    date: "2024-01-15",
    duration: "45 دقيقة",
    fileUrl: "/lectures/lecture1.pdf",
    videoUrl: "https://youtube.com/watch?v=example1",
    isPublished: true
  },
  {
    id: "2",
    title: "المتغيرات وأنواع البيانات",
    description: "شرح المتغيرات وأنواع البيانات في Python",
    subject: "مقدمة في البرمجة",
    teacher: "د. أحمد محمد",
    date: "2024-01-20",
    duration: "50 دقيقة",
    fileUrl: "/lectures/lecture2.pdf",
    videoUrl: "https://youtube.com/watch?v=example2",
    isPublished: true
  },
  {
    id: "3",
    title: "مقدمة في قواعد البيانات",
    description: "التعريف بقواعد البيانات وأهميتها",
    subject: "قواعد البيانات",
    teacher: "د. فاطمة علي",
    date: "2024-01-18",
    duration: "60 دقيقة",
    fileUrl: "/lectures/lecture3.pdf",
    videoUrl: null,
    isPublished: true
  }
]

export default function LecturesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [filteredLectures, setFilteredLectures] = useState(mockLectures)

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/login")
      return
    }
  }, [session, status, router])

  useEffect(() => {
    const filtered = mockLectures.filter(lecture =>
      lecture.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lecture.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lecture.teacher.toLowerCase().includes(searchTerm.toLowerCase())
    )
    setFilteredLectures(filtered)
  }, [searchTerm])

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard" className="flex items-center text-blue-600 hover:text-blue-700">
                <ArrowRight className="h-5 w-5 ml-2" />
                العودة للوحة التحكم
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-blue-600 text-white p-2 rounded-lg">
                <BookOpen className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">المحاضرات</h1>
                <p className="text-sm text-gray-600">جميع المحاضرات المتاحة</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Bar */}
        <div className="mb-8">
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="البحث في المحاضرات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Lectures Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredLectures.map((lecture) => (
            <Card key={lecture.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <BookOpen className="h-5 w-5 text-blue-600" />
                  </div>
                  <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                    متاح
                  </span>
                </div>
                <CardTitle className="text-lg">{lecture.title}</CardTitle>
                <CardDescription>{lecture.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <BookOpen className="h-4 w-4 ml-2" />
                    {lecture.subject}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <User className="h-4 w-4 ml-2" />
                    {lecture.teacher}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 ml-2" />
                    {new Date(lecture.date).toLocaleDateString('ar-EG')} • {lecture.duration}
                  </div>
                  
                  <div className="flex space-x-2 pt-4">
                    {lecture.fileUrl && (
                      <Button size="sm" className="flex-1">
                        <Download className="h-4 w-4 ml-2" />
                        تحميل PDF
                      </Button>
                    )}
                    {lecture.videoUrl && (
                      <Button size="sm" variant="outline" className="flex-1">
                        <Play className="h-4 w-4 ml-2" />
                        مشاهدة
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredLectures.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد محاضرات</h3>
            <p className="text-gray-600">لم يتم العثور على محاضرات تطابق البحث</p>
          </div>
        )}

        {/* Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 p-3 rounded-full">
                  <BookOpen className="h-6 w-6 text-blue-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">{mockLectures.length}</p>
                  <p className="text-gray-600">إجمالي المحاضرات</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="bg-green-100 p-3 rounded-full">
                  <Play className="h-6 w-6 text-green-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {mockLectures.filter(l => l.videoUrl).length}
                  </p>
                  <p className="text-gray-600">محاضرات فيديو</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 p-3 rounded-full">
                  <Download className="h-6 w-6 text-purple-600" />
                </div>
                <div className="mr-4">
                  <p className="text-2xl font-bold text-gray-900">
                    {mockLectures.filter(l => l.fileUrl).length}
                  </p>
                  <p className="text-gray-600">ملفات PDF</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
