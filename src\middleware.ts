import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // السماح بالوصول للصفحات العامة
    if (pathname === "/" || pathname === "/login") {
      return NextResponse.next()
    }

    // التحقق من وجود المصادقة
    if (!token) {
      return NextResponse.redirect(new URL("/login", req.url))
    }

    // التحقق من صلاحيات الوصول للوحات التحكم
    if (pathname.startsWith("/dashboard/admin") && token.role !== "ADMIN") {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }

    if (pathname.startsWith("/dashboard/teacher") && token.role !== "TEACHER") {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }

    if (pathname.startsWith("/dashboard/student") && token.role !== "STUDENT") {
      return NextResponse.redirect(new URL("/dashboard", req.url))
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl
        
        // السماح بالوصول للصفحات العامة
        if (pathname === "/" || pathname === "/login") {
          return true
        }

        // التحقق من وجود token للصفحات المحمية
        return !!token
      }
    }
  }
)

export const config = {
  matcher: [
    "/dashboard/:path*",
    "/lectures/:path*",
    "/assignments/:path*",
    "/messages/:path*"
  ]
}
