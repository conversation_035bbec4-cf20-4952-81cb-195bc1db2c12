"use strict";var i=Object.defineProperty;var s=(e,t)=>i(e,"name",{value:t,configurable:!0});const n=s((e,t)=>{const r=e[0]-t[0];if(r===0){const o=e[1]-t[1];return o===0?e[2]>=t[2]:o>0}return r>0},"isVersionGreaterOrEqual"),u=process.versions.node.split(".").map(Number),l=s((e,t=u)=>{for(let r=0;r<e.length;r+=1){const o=e[r];if(r===e.length-1||t[0]===o[0])return n(t,o)}return!1},"isFeatureSupported"),a=[[18,19,0],[20,6,0]],c=[[18,19,0],[20,10,0],[21,0,0]],d=[[21,0,0]],f=[[20,11,0],[21,3,0]];exports.esmLoadReadFile=f,exports.importAttributes=c,exports.isFeatureSupported=l,exports.moduleRegister=a,exports.testRunnerGlob=d;
