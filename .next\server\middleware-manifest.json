{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/lectures(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/lectures/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/assignments(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/assignments/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/messages(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/messages/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qrUUpV8JtBqJAAYOWjHh1Co06QbvQXmcWVuQrBJ4WkU=", "__NEXT_PREVIEW_MODE_ID": "bab312b78adfe362a534b2e2aa128cb7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b39df801748bcf38c15b9d04e1c18857b179a58b9293e02b6dcd39bef73c992e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3bf5992470c489c807bf793f50ef3714ce4778f844bcffe01d915c04e3dbd167"}}}, "sortedMiddleware": ["/"], "functions": {}}