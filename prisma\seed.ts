import { PrismaClient } from '../src/generated/prisma'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 بدء إضافة البيانات التجريبية...')

  // إنشاء المستخدمين الأساسيين
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'مدير النظام',
      role: 'ADMIN'
    }
  })

  const teacherUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'د. أحمد محمد',
      role: 'TEACHER'
    }
  })

  const studentUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'محمد علي أحمد',
      role: 'STUDENT'
    }
  })

  console.log('✅ تم إضافة البيانات التجريبية بنجاح!')
  console.log(`👤 المستخدمون: ${await prisma.user.count()}`)
}

main()
  .catch((e) => {
    console.error('❌ خطأ في إضافة البيانات:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
