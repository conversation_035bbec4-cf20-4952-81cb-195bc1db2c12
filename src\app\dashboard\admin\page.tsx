"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  BookOpen, 
  Users, 
  UserCheck, 
  GraduationCap, 
  BarChart3, 
  Settings, 
  Plus, 
  User, 
  LogOut,
  FileText,
  Database,
  Shield
} from "lucide-react"

export default function AdminDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/login")
      return
    }

    if (session.user.role !== "ADMIN") {
      router.push("/dashboard")
      return
    }
  }, [session, status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!session || session.user.role !== "ADMIN") {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="bg-purple-600 text-white p-2 rounded-lg">
                <Shield className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">لوحة تحكم الإدارة</h1>
                <p className="text-sm text-gray-600">مرحباً، {session.user.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                <Plus className="h-4 w-4 ml-2" />
                إضافة مستخدم
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 ml-2" />
                الإعدادات
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => signOut({ callbackUrl: "/" })}
              >
                <LogOut className="h-4 w-4 ml-2" />
                تسجيل الخروج
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الطلاب</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,247</div>
              <p className="text-xs text-muted-foreground">+12% من الشهر الماضي</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">أعضاء هيئة التدريس</CardTitle>
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">45</div>
              <p className="text-xs text-muted-foreground">+2 مدرس جديد</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المواد الدراسية</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">128</div>
              <p className="text-xs text-muted-foreground">في 4 فرق دراسية</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المستخدمون النشطون</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">892</div>
              <p className="text-xs text-muted-foreground">في آخر 24 ساعة</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* System Overview */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>نظرة عامة على النظام</CardTitle>
              <CardDescription>إحصائيات شاملة للمنصة التعليمية</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Students by Year */}
                <div>
                  <h4 className="text-sm font-medium mb-3">توزيع الطلاب حسب السنة الدراسية</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">الفرقة الأولى</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{ width: "85%" }}></div>
                        </div>
                        <span className="text-sm font-medium">340</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">الفرقة الثانية</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div className="bg-green-600 h-2 rounded-full" style={{ width: "75%" }}></div>
                        </div>
                        <span className="text-sm font-medium">298</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">الفرقة الثالثة</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div className="bg-purple-600 h-2 rounded-full" style={{ width: "70%" }}></div>
                        </div>
                        <span className="text-sm font-medium">276</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">الفرقة الرابعة</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div className="bg-orange-600 h-2 rounded-full" style={{ width: "65%" }}></div>
                        </div>
                        <span className="text-sm font-medium">333</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Recent Activities */}
                <div>
                  <h4 className="text-sm font-medium mb-3">الأنشطة الأخيرة</h4>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 text-sm">
                      <div className="bg-blue-100 p-1 rounded-full">
                        <Users className="h-3 w-3 text-blue-600" />
                      </div>
                      <span>تم تسجيل 5 طلاب جدد</span>
                      <span className="text-gray-500 text-xs">منذ ساعة</span>
                    </div>
                    <div className="flex items-center space-x-3 text-sm">
                      <div className="bg-green-100 p-1 rounded-full">
                        <GraduationCap className="h-3 w-3 text-green-600" />
                      </div>
                      <span>تم إضافة مدرس جديد للقسم</span>
                      <span className="text-gray-500 text-xs">منذ 3 ساعات</span>
                    </div>
                    <div className="flex items-center space-x-3 text-sm">
                      <div className="bg-purple-100 p-1 rounded-full">
                        <BookOpen className="h-3 w-3 text-purple-600" />
                      </div>
                      <span>تم إنشاء 3 مواد دراسية جديدة</span>
                      <span className="text-gray-500 text-xs">منذ يوم</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Admin Tools */}
          <Card>
            <CardHeader>
              <CardTitle>أدوات الإدارة</CardTitle>
              <CardDescription>إدارة شاملة للنظام</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button className="w-full justify-start" size="sm">
                  <Users className="h-4 w-4 ml-2" />
                  إدارة المستخدمين
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <BookOpen className="h-4 w-4 ml-2" />
                  إدارة المواد الدراسية
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <GraduationCap className="h-4 w-4 ml-2" />
                  إدارة أعضاء هيئة التدريس
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <BarChart3 className="h-4 w-4 ml-2" />
                  التقارير والإحصائيات
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Database className="h-4 w-4 ml-2" />
                  إدارة قاعدة البيانات
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Settings className="h-4 w-4 ml-2" />
                  إعدادات النظام
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <FileText className="h-4 w-4 ml-2" />
                  سجلات النظام
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Users */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>المستخدمون الجدد</CardTitle>
              <CardDescription>آخر المستخدمين المسجلين في النظام</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">أحمد محمد علي</h4>
                      <p className="text-sm text-gray-500">طالب - الفرقة الأولى شعبة أ</p>
                      <p className="text-xs text-gray-400">تم التسجيل منذ ساعة</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">عرض</Button>
                    <Button size="sm">تفعيل</Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="bg-green-100 p-2 rounded-full">
                      <GraduationCap className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">د. فاطمة حسن</h4>
                      <p className="text-sm text-gray-500">مدرس - قسم علوم الحاسب</p>
                      <p className="text-xs text-gray-400">تم التسجيل منذ 3 ساعات</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">عرض</Button>
                    <Button size="sm">تفعيل</Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="bg-purple-100 p-2 rounded-full">
                      <User className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">سارة أحمد محمود</h4>
                      <p className="text-sm text-gray-500">طالبة - الفرقة الثانية شعبة ب</p>
                      <p className="text-xs text-gray-400">تم التسجيل منذ 5 ساعات</p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">عرض</Button>
                    <Button size="sm">تفعيل</Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
