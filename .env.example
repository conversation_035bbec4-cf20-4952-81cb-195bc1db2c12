# Database Configuration
# Replace with your actual PostgreSQL connection string
DATABASE_URL="postgresql://username:password@localhost:5432/himconline?schema=public"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here-change-this-in-production"

# Supabase Configuration (Optional - for production)
# NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
# NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
# SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# File Upload Configuration (Optional)
# CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
# CLOUDINARY_API_KEY="your-cloudinary-api-key"
# CLOUDINARY_API_SECRET="your-cloudinary-api-secret"
