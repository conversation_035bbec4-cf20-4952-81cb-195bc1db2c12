"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BookOpen, Award, MessageSquare, Users, Plus, Upload, User, LogOut, FileText, Calendar } from "lucide-react"

export default function TeacherDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/login")
      return
    }

    if (session.user.role !== "TEACHER") {
      router.push("/dashboard")
      return
    }
  }, [session, status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!session || session.user.role !== "TEACHER") {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="bg-green-600 text-white p-2 rounded-lg">
                <BookOpen className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">لوحة تحكم المدرس</h1>
                <p className="text-sm text-gray-600">مرحباً، {session.user.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button size="sm" className="bg-green-600 hover:bg-green-700">
                <Plus className="h-4 w-4 ml-2" />
                إضافة محاضرة
              </Button>
              <Button variant="outline" size="sm">
                <User className="h-4 w-4 ml-2" />
                الملف الشخصي
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => signOut({ callbackUrl: "/" })}
              >
                <LogOut className="h-4 w-4 ml-2" />
                تسجيل الخروج
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الطلاب</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">156</div>
              <p className="text-xs text-muted-foreground">في جميع المواد</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المحاضرات المنشورة</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">42</div>
              <p className="text-xs text-muted-foreground">محاضرة هذا الفصل</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">التكليفات النشطة</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">تكليف يحتاج مراجعة</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الرسائل الجديدة</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">رسالة غير مقروءة</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activities */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>الأنشطة الأخيرة</CardTitle>
              <CardDescription>آخر الأنشطة في المواد التي تدرسها</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <BookOpen className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">تم رفع محاضرة جديدة</h4>
                      <p className="text-sm text-gray-500">مقدمة في البرمجة - المحاضرة 5</p>
                      <p className="text-xs text-gray-400">منذ ساعتين</p>
                    </div>
                  </div>
                  <Button size="sm" variant="outline">عرض</Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="bg-green-100 p-2 rounded-full">
                      <Award className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">تم تسليم تكليف جديد</h4>
                      <p className="text-sm text-gray-500">مشروع قاعدة البيانات - أحمد محمد</p>
                      <p className="text-xs text-gray-400">منذ 3 ساعات</p>
                    </div>
                  </div>
                  <Button size="sm" variant="outline">مراجعة</Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="bg-purple-100 p-2 rounded-full">
                      <MessageSquare className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">رسالة جديدة من طالب</h4>
                      <p className="text-sm text-gray-500">استفسار حول المحاضرة الأخيرة</p>
                      <p className="text-xs text-gray-400">منذ 5 ساعات</p>
                    </div>
                  </div>
                  <Button size="sm" variant="outline">رد</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>الإجراءات السريعة</CardTitle>
              <CardDescription>أدوات سريعة للإدارة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button className="w-full justify-start" size="sm">
                  <Plus className="h-4 w-4 ml-2" />
                  إضافة محاضرة جديدة
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Award className="h-4 w-4 ml-2" />
                  إنشاء تكليف جديد
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Upload className="h-4 w-4 ml-2" />
                  رفع ملفات
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Users className="h-4 w-4 ml-2" />
                  إدارة الطلاب
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <MessageSquare className="h-4 w-4 ml-2" />
                  الرسائل
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <FileText className="h-4 w-4 ml-2" />
                  التقارير
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Subjects Overview */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">المواد التي تدرسها</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">مقدمة في البرمجة</CardTitle>
                <CardDescription>الفرقة الأولى - شعبة أ</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>عدد الطلاب:</span>
                    <span className="font-medium">45</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>المحاضرات:</span>
                    <span className="font-medium">12</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>التكليفات:</span>
                    <span className="font-medium">3</span>
                  </div>
                  <Button size="sm" className="w-full mt-3">
                    <Calendar className="h-4 w-4 ml-2" />
                    إدارة المادة
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">قواعد البيانات</CardTitle>
                <CardDescription>الفرقة الثانية - شعبة ب</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>عدد الطلاب:</span>
                    <span className="font-medium">38</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>المحاضرات:</span>
                    <span className="font-medium">8</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>التكليفات:</span>
                    <span className="font-medium">2</span>
                  </div>
                  <Button size="sm" className="w-full mt-3">
                    <Calendar className="h-4 w-4 ml-2" />
                    إدارة المادة
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">تطوير المواقع</CardTitle>
                <CardDescription>الفرقة الثالثة - شعبة أ</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>عدد الطلاب:</span>
                    <span className="font-medium">42</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>المحاضرات:</span>
                    <span className="font-medium">15</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>التكليفات:</span>
                    <span className="font-medium">4</span>
                  </div>
                  <Button size="sm" className="w-full mt-3">
                    <Calendar className="h-4 w-4 ml-2" />
                    إدارة المادة
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
