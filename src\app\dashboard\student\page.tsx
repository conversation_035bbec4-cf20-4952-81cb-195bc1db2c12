"use client"

import { useSession, signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BookOpen, Award, MessageSquare, Bell, Calendar, Download, User, LogOut } from "lucide-react"

export default function StudentDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "loading") return

    if (!session) {
      router.push("/login")
      return
    }

    if (session.user.role !== "STUDENT") {
      router.push("/dashboard")
      return
    }
  }, [session, status, router])

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!session || session.user.role !== "STUDENT") {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-600 text-white p-2 rounded-lg">
                <BookOpen className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">لوحة تحكم الطالب</h1>
                <p className="text-sm text-gray-600">مرحباً، {session.user.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Bell className="h-4 w-4 ml-2" />
                الإشعارات
              </Button>
              <Button variant="outline" size="sm">
                <User className="h-4 w-4 ml-2" />
                الملف الشخصي
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => signOut({ callbackUrl: "/" })}
              >
                <LogOut className="h-4 w-4 ml-2" />
                تسجيل الخروج
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المحاضرات المتاحة</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24</div>
              <p className="text-xs text-muted-foreground">محاضرة جديدة هذا الأسبوع</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">التكليفات النشطة</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">5</div>
              <p className="text-xs text-muted-foreground">تكليف يجب تسليمه</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">الرسائل الجديدة</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">رسالة غير مقروءة</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المعدل التراكمي</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3.8</div>
              <p className="text-xs text-muted-foreground">من 4.0</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Lectures */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>المحاضرات الأخيرة</CardTitle>
              <CardDescription>آخر المحاضرات المضافة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <BookOpen className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">مقدمة في البرمجة - المحاضرة 5</h4>
                      <p className="text-sm text-gray-500">د. أحمد محمد • منذ ساعتين</p>
                    </div>
                  </div>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4 ml-2" />
                    تحميل
                  </Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="bg-green-100 p-2 rounded-full">
                      <BookOpen className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">قواعد البيانات - المحاضرة 3</h4>
                      <p className="text-sm text-gray-500">د. فاطمة علي • منذ يوم واحد</p>
                    </div>
                  </div>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4 ml-2" />
                    تحميل
                  </Button>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="bg-purple-100 p-2 rounded-full">
                      <BookOpen className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-medium">الرياضيات التطبيقية - المحاضرة 7</h4>
                      <p className="text-sm text-gray-500">د. محمد حسن • منذ يومين</p>
                    </div>
                  </div>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4 ml-2" />
                    تحميل
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Assignments */}
          <Card>
            <CardHeader>
              <CardTitle>التكليفات القادمة</CardTitle>
              <CardDescription>مواعيد التسليم القريبة</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-3 border rounded-lg border-red-200 bg-red-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-sm">مشروع قاعدة البيانات</h4>
                    <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded">عاجل</span>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">موعد التسليم: غداً</p>
                  <Button size="sm" className="w-full">
                    <Calendar className="h-4 w-4 ml-2" />
                    عرض التفاصيل
                  </Button>
                </div>

                <div className="p-3 border rounded-lg border-yellow-200 bg-yellow-50">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-sm">تمارين البرمجة</h4>
                    <span className="text-xs text-yellow-600 bg-yellow-100 px-2 py-1 rounded">قريب</span>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">موعد التسليم: خلال 3 أيام</p>
                  <Button size="sm" variant="outline" className="w-full">
                    <Calendar className="h-4 w-4 ml-2" />
                    عرض التفاصيل
                  </Button>
                </div>

                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-sm">بحث الرياضيات</h4>
                    <span className="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded">عادي</span>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">موعد التسليم: خلال أسبوع</p>
                  <Button size="sm" variant="outline" className="w-full">
                    <Calendar className="h-4 w-4 ml-2" />
                    عرض التفاصيل
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">الإجراءات السريعة</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button className="h-20 flex-col space-y-2">
              <BookOpen className="h-6 w-6" />
              <span>عرض المحاضرات</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <Award className="h-6 w-6" />
              <span>التكليفات</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <MessageSquare className="h-6 w-6" />
              <span>الرسائل</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <Calendar className="h-6 w-6" />
              <span>الجدول الدراسي</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
