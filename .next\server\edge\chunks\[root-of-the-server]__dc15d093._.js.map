{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from \"next-auth/middleware\"\nimport { NextResponse } from \"next/server\"\n\nexport default withAuth(\n  function middleware(req) {\n    const token = req.nextauth.token\n    const { pathname } = req.nextUrl\n\n    // السماح بالوصول للصفحات العامة\n    if (pathname === \"/\" || pathname === \"/login\") {\n      return NextResponse.next()\n    }\n\n    // التحقق من وجود المصادقة\n    if (!token) {\n      return NextResponse.redirect(new URL(\"/login\", req.url))\n    }\n\n    // التحقق من صلاحيات الوصول للوحات التحكم\n    if (pathname.startsWith(\"/dashboard/admin\") && token.role !== \"ADMIN\") {\n      return NextResponse.redirect(new URL(\"/dashboard\", req.url))\n    }\n\n    if (pathname.startsWith(\"/dashboard/teacher\") && token.role !== \"TEACHER\") {\n      return NextResponse.redirect(new URL(\"/dashboard\", req.url))\n    }\n\n    if (pathname.startsWith(\"/dashboard/student\") && token.role !== \"STUDENT\") {\n      return NextResponse.redirect(new URL(\"/dashboard\", req.url))\n    }\n\n    return NextResponse.next()\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        const { pathname } = req.nextUrl\n        \n        // السماح بالوصول للصفحات العامة\n        if (pathname === \"/\" || pathname === \"/login\") {\n          return true\n        }\n\n        // التحقق من وجود token للصفحات المحمية\n        return !!token\n      }\n    }\n  }\n)\n\nexport const config = {\n  matcher: [\n    \"/dashboard/:path*\",\n    \"/lectures/:path*\",\n    \"/assignments/:path*\",\n    \"/messages/:path*\"\n  ]\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;IACrB,MAAM,QAAQ,IAAI,QAAQ,CAAC,KAAK;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;IAEhC,gCAAgC;IAChC,IAAI,aAAa,OAAO,aAAa,UAAU;QAC7C,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,0BAA0B;IAC1B,IAAI,CAAC,OAAO;QACV,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;IACxD;IAEA,yCAAyC;IACzC,IAAI,SAAS,UAAU,CAAC,uBAAuB,MAAM,IAAI,KAAK,SAAS;QACrE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,IAAI,SAAS,UAAU,CAAC,yBAAyB,MAAM,IAAI,KAAK,WAAW;QACzE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,IAAI,SAAS,UAAU,CAAC,yBAAyB,MAAM,IAAI,KAAK,WAAW;QACzE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,OAAO;YAEhC,gCAAgC;YAChC,IAAI,aAAa,OAAO,aAAa,UAAU;gBAC7C,OAAO;YACT;YAEA,uCAAuC;YACvC,OAAO,CAAC,CAAC;QACX;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;QACA;KACD;AACH"}}]}