// مخطط قاعدة البيانات للمنصة التعليمية - المعهد العالي للإدارة والحاسب الآلي
// Database schema for Educational Platform - Higher Institute of Management and Computer Science

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// نموذج المستخدم الأساسي
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  role      UserRole @default(STUDENT)
  avatar    String?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // العلاقات
  student   Student?
  teacher   Teacher?
  admin     Admin?
  sessions  Session[]
  accounts  Account[]

  @@map("users")
}

// أدوار المستخدمين
enum UserRole {
  STUDENT
  TEACHER
  ADMIN
}

// نموذج الطالب
model Student {
  id             String @id @default(cuid())
  userId         String @unique
  studentNumber  String @unique // الرقم الجامعي
  yearLevel      Int    // السنة الدراسية (1-4)
  section        String // الشعبة (أ، ب)

  // العلاقات
  user           User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  assignments    AssignmentSubmission[]
  messages       Message[]
  notifications  Notification[]

  @@map("students")
}

// نموذج المدرس
model Teacher {
  id          String @id @default(cuid())
  userId      String @unique
  employeeId  String @unique
  department  String?

  // العلاقات
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  subjects    Subject[]
  lectures    Lecture[]
  assignments Assignment[]
  messages    Message[]

  @@map("teachers")
}

// نموذج المشرف الإداري
model Admin {
  id       String @id @default(cuid())
  userId   String @unique
  position String?

  // العلاقات
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admins")
}

// نموذج المواد الدراسية
model Subject {
  id          String @id @default(cuid())
  name        String
  code        String @unique
  description String?
  yearLevel   Int    // السنة الدراسية
  section     String // الشعبة
  semester    String // الفصل الدراسي
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  teacherId   String
  teacher     Teacher      @relation(fields: [teacherId], references: [id])
  lectures    Lecture[]
  assignments Assignment[]

  @@map("subjects")
}

// نموذج المحاضرات
model Lecture {
  id          String   @id @default(cuid())
  title       String
  description String?
  content     String?  // محتوى المحاضرة
  fileUrl     String?  // رابط ملف PDF
  videoUrl    String?  // رابط الفيديو
  order       Int      // ترتيب المحاضرة
  isPublished Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  subjectId   String
  subject     Subject @relation(fields: [subjectId], references: [id])
  teacherId   String
  teacher     Teacher @relation(fields: [teacherId], references: [id])

  @@map("lectures")
}

// نموذج التكليفات
model Assignment {
  id          String   @id @default(cuid())
  title       String
  description String
  instructions String?
  dueDate     DateTime
  maxGrade    Float    @default(100)
  isPublished Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // العلاقات
  subjectId   String
  subject     Subject @relation(fields: [subjectId], references: [id])
  teacherId   String
  teacher     Teacher @relation(fields: [teacherId], references: [id])
  submissions AssignmentSubmission[]

  @@map("assignments")
}

// نموذج تسليم التكليفات
model AssignmentSubmission {
  id           String   @id @default(cuid())
  content      String?  // محتوى الإجابة
  fileUrl      String?  // رابط الملف المرفوع
  grade        Float?   // الدرجة
  feedback     String?  // تعليق المدرس
  submittedAt  DateTime @default(now())
  gradedAt     DateTime?

  // العلاقات
  assignmentId String
  assignment   Assignment @relation(fields: [assignmentId], references: [id])
  studentId    String
  student      Student    @relation(fields: [studentId], references: [id])

  @@unique([assignmentId, studentId])
  @@map("assignment_submissions")
}

// نموذج الرسائل
model Message {
  id        String      @id @default(cuid())
  content   String
  type      MessageType @default(GENERAL)
  isRead    Boolean     @default(false)
  createdAt DateTime    @default(now())

  // العلاقات - المرسل
  senderId  String?
  sender    Teacher?    @relation(fields: [senderId], references: [id])

  // العلاقات - المستقبل
  studentId String?
  student   Student?    @relation(fields: [studentId], references: [id])

  @@map("messages")
}

// أنواع الرسائل
enum MessageType {
  GENERAL
  ASSIGNMENT
  LECTURE
  ANNOUNCEMENT
}

// نموذج الإشعارات
model Notification {
  id        String           @id @default(cuid())
  title     String
  content   String
  type      NotificationType
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())

  // العلاقات
  studentId String
  student   Student @relation(fields: [studentId], references: [id])

  @@map("notifications")
}

// أنواع الإشعارات
enum NotificationType {
  NEW_LECTURE
  NEW_ASSIGNMENT
  ASSIGNMENT_DUE
  GRADE_POSTED
  ANNOUNCEMENT
}

// نماذج المصادقة (NextAuth.js)
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}
