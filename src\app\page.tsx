import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BookOpen, Users, MessageSquare, Award, Clock, Shield } from "lucide-react"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-600 text-white p-2 rounded-lg">
                <BookOpen className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">المعهد العالي للإدارة والحاسب الآلي</h1>
                <p className="text-sm text-gray-600">المنصة التعليمية الإلكترونية</p>
              </div>
            </div>
            <Link href="/login">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                تسجيل الدخول
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            منصة تعليمية متطورة
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            منصة شاملة لإدارة التعليم الإلكتروني تسهل على الطلاب متابعة المحاضرات وتسليم التكليفات والتواصل مع أعضاء هيئة التدريس
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/login">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-3">
                ابدأ الآن
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="text-lg px-8 py-3">
              تعرف على المزيد
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">مميزات المنصة</h3>
            <p className="text-lg text-gray-600">كل ما تحتاجه للتعليم الإلكتروني في مكان واحد</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto bg-blue-100 text-blue-600 p-3 rounded-full w-fit mb-4">
                  <BookOpen className="h-8 w-8" />
                </div>
                <CardTitle>إدارة المحاضرات</CardTitle>
                <CardDescription>
                  رفع وتنظيم المحاضرات بصيغ مختلفة مع إمكانية الوصول السهل للطلاب
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto bg-green-100 text-green-600 p-3 rounded-full w-fit mb-4">
                  <Award className="h-8 w-8" />
                </div>
                <CardTitle>نظام التكليفات</CardTitle>
                <CardDescription>
                  إنشاء وإدارة التكليفات مع نظام تقييم شامل وتعليقات تفصيلية
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto bg-purple-100 text-purple-600 p-3 rounded-full w-fit mb-4">
                  <MessageSquare className="h-8 w-8" />
                </div>
                <CardTitle>التواصل المباشر</CardTitle>
                <CardDescription>
                  نظام رسائل متطور للتواصل بين الطلاب وأعضاء هيئة التدريس
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto bg-orange-100 text-orange-600 p-3 rounded-full w-fit mb-4">
                  <Users className="h-8 w-8" />
                </div>
                <CardTitle>إدارة المستخدمين</CardTitle>
                <CardDescription>
                  نظام شامل لإدارة الطلاب والمدرسين مع صلاحيات متدرجة
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto bg-red-100 text-red-600 p-3 rounded-full w-fit mb-4">
                  <Clock className="h-8 w-8" />
                </div>
                <CardTitle>الإشعارات الذكية</CardTitle>
                <CardDescription>
                  تنبيهات فورية للمحاضرات الجديدة ومواعيد تسليم التكليفات
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto bg-indigo-100 text-indigo-600 p-3 rounded-full w-fit mb-4">
                  <Shield className="h-8 w-8" />
                </div>
                <CardTitle>الأمان والخصوصية</CardTitle>
                <CardDescription>
                  حماية متقدمة للبيانات مع نظام مصادقة آمن ومتعدد المستويات
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-4 mb-6">
              <div className="bg-blue-600 text-white p-2 rounded-lg">
                <BookOpen className="h-6 w-6" />
              </div>
              <h4 className="text-xl font-bold">المعهد العالي للإدارة والحاسب الآلي</h4>
            </div>
            <p className="text-gray-400 mb-4">
              منصة تعليمية متطورة لخدمة طلاب وأعضاء هيئة التدريس
            </p>
            <p className="text-sm text-gray-500">
              © 2024 جميع الحقوق محفوظة - المعهد العالي للإدارة والحاسب الآلي
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
